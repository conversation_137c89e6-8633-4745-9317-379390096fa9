# Chrome Web Store 商店描述

## 英文版本 (English)

### Short Description (132 characters max)
```
Automatically detects and renders escaped markdown content on web pages for better readability
```

### Detailed Description
```
🔍 Markdown Unescape Viewer - Transform Escaped Markdown into Beautiful Content!

Tired of seeing content like this on web pages?
\*\*Important Notice\*\* Please check the \`config.json\` file

Now this extension automatically detects and displays below:
**Important Notice** Please check the `config.json` file

✨ Key Features:
• 🔍 Auto-detect escaped markdown content
• 📝 Real-time rendering with beautiful formatting
• 🎨 Dark mode support
• ⚡ Lightweight, no performance impact
• 🎛️ One-click enable/disable
• 📱 Responsive design
• 🔒 Complete privacy protection

🎯 Supported Escape Formats:
• \*bold\* → **bold**
• \_italic\_ → _italic_
• \`code\` → `code`
• \# headers → # headers
• \\n newlines
• And many more...

🚀 Perfect for:
• API documentation viewing
• Technical forum browsing
• Code comment reading
• JSON response viewing
• Any webpage with escaped markdown

💡 Works out of the box!
Extension is enabled by default, intelligently detects page content, and provides a better reading experience.

🔒 Privacy First:
• No data collection
• Works only on current tab
• Completely local processing
• Open source code

Install now and make escaped markdown content readable!
```

## 中文版本 (Chinese)

### 简短描述 (132字符以内)
```
自动检测并渲染网页中的转义Markdown内容，提升阅读体验
```

### 详细描述
```
🔍 Markdown转义查看器 - 让转义的Markdown重获新生！

厌倦了在网页上看到这样的内容吗？
\*\*重要提醒\*\* 请检查 \`config.json\` 文件

现在，这个插件会自动检测并在下方显示：
**重要提醒** 请检查 `config.json` 文件

✨ 主要功能：
• 🔍 自动检测转义的Markdown内容
• 📝 实时渲染为美观的格式
• 🎨 支持深色模式
• ⚡ 轻量级，不影响页面性能
• 🎛️ 一键开启/关闭
• 📱 响应式设计
• 🔒 完全隐私保护

🎯 支持的转义格式：
• \*粗体\* → **粗体**
• \_斜体\_ → _斜体_
• \`代码\` → `代码`
• \# 标题 → # 标题
• \\n 换行符
• 以及更多...

🚀 适用场景：
• API文档查看
• 技术论坛浏览
• 代码注释阅读
• JSON响应查看
• 任何包含转义Markdown的网页

💡 安装即用，无需配置！
插件默认启用，智能检测页面内容，为您提供更好的阅读体验。

🔒 隐私优先：
• 不收集任何数据
• 仅在当前标签页工作
• 完全本地处理
• 开源代码

立即安装，让转义的Markdown内容变得易读！
```

## 关键词标签

### English Keywords
```
markdown, developer tools, productivity, text processing, web development, documentation, readability, formatting, escape characters, renderer
```

### Chinese Keywords
```
markdown, 开发工具, 生产力, 文本处理, 网页开发, 文档, 可读性, 格式化, 转义字符, 渲染器
```

## 分类建议

### Primary Category
- **Developer Tools** (开发者工具)

### Secondary Categories
- Productivity (生产力)
- Accessibility (无障碍)

## 目标用户群体

### Primary Users
- Web developers
- Technical writers
- API documentation readers
- Software engineers
- DevOps engineers

### Secondary Users
- Technical blog readers
- Students learning programming
- Anyone dealing with escaped text content

## 竞争优势

1. **专注性**: 专门解决转义Markdown问题
2. **隐私**: 完全本地处理，不收集数据
3. **易用性**: 安装即用，无需配置
4. **性能**: 轻量级，不影响页面加载
5. **开源**: 代码透明，可信赖

## 定价策略

**建议**: 免费
- 吸引更多用户
- 建立用户基础
- 展示技术能力
- 后续可考虑高级功能版本

## 发布地区

**建议**: 全球发布
- 英语国家 (美国、英国、加拿大、澳大利亚等)
- 中文地区 (中国大陆、台湾、香港等)
- 其他技术发达地区 (日本、韩国、德国、法国等)

## 版本更新计划

### v1.0.0 (首次发布)
- 基础功能
- 英文界面
- 基本转义支持

### v1.1.0 (计划)
- 多语言支持
- 更多转义格式
- 性能优化

### v1.2.0 (计划)
- 自定义样式
- 更多配置选项
- 用户反馈功能
