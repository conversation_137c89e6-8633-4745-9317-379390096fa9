# Chrome Web Store 发布资料

## 📸 所需截图尺寸

### 必需截图
- **1280x800** 像素 (最多5张)
- 展示插件的主要功能
- 高质量，清晰的界面截图

### 可选宣传图片
- **440x280** 像素 (小型宣传图片)
- **920x680** 像素 (大型宣传图片) 
- **1400x560** 像素 (侯爵宣传图片)

## 📝 商店描述文案

### 简短描述 (132字符以内)
```
Automatically detects and renders escaped markdown content on web pages for better readability
```

### 详细描述
```
🔍 Markdown Unescape Viewer - 让转义的Markdown内容重获新生！

厌倦了在网页上看到这样的内容吗？
\*\*重要提醒\*\* 请检查 \`config.json\` 文件

现在，这个插件会自动检测并在下方显示：
**重要提醒** 请检查 `config.json` 文件

✨ 主要功能：
• 🔍 自动检测转义的Markdown内容
• 📝 实时渲染为美观的格式
• 🎨 支持深色模式
• ⚡ 轻量级，不影响页面性能
• 🎛️ 一键开启/关闭
• 📱 响应式设计

🎯 支持的转义格式：
• \*粗体\* → **粗体**
• \_斜体\_ → _斜体_
• \`代码\` → `代码`
• \# 标题 → # 标题
• \\n 换行符
• 以及更多...

🚀 使用场景：
• API文档查看
• 技术论坛浏览
• 代码注释阅读
• JSON响应查看
• 任何包含转义Markdown的网页

💡 安装即用，无需配置！
插件默认启用，智能检测页面内容，为您提供更好的阅读体验。

🔒 隐私保护：
• 不收集任何个人数据
• 仅在当前标签页工作
• 完全本地处理

立即安装，让转义的Markdown内容变得易读！
```

## 🏷️ 标签建议
- markdown
- developer tools
- productivity
- text processing
- web development
- documentation
- readability
- formatting

## 📂 文件清单

### 必需文件
- [ ] manifest.json (已完成)
- [ ] 所有源代码文件 (已完成)
- [ ] 128x128 图标 (已有)
- [ ] 商店截图 (需要创建)

### 推荐文件
- [ ] 隐私政策
- [ ] 使用条款
- [ ] 支持页面

## 🎯 目标用户
- 开发者
- 技术文档阅读者
- API用户
- 技术博客读者
- 任何经常遇到转义Markdown的用户

## 💰 定价策略
建议：**免费**
- 吸引更多用户
- 建立用户基础
- 后续可考虑高级功能

## 📊 关键词优化
主要关键词：
- markdown viewer
- escape characters
- text formatting
- developer tools
- markdown renderer
