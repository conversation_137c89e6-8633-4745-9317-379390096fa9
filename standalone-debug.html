<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Standalone Debug Test</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        .escaped-content {
            background: #fff3e0;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #ff9800;
            font-family: monospace;
        }
        pre.test-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 Standalone Debug Test</h1>
    
    <div class="debug-info">
        <strong>Processing Status:</strong> <span id="processing-status">Initializing...</span><br>
        <strong>Containers Created:</strong> <span id="container-count">0</span><br>
        <strong>Last Update:</strong> <span id="last-update">Never</span><br>
        <button id="toggle-btn" onclick="toggleExtension()">Disable Extension</button>
    </div>

    <div class="test-section">
        <h2>Test 1: Basic Markdown Escapes</h2>
        <div class="escaped-content">
            This text contains \*bold text\* and \_italic text\_ and \`inline code\`.
        </div>
    </div>

    <div class="test-section">
        <h2>Test 2: Headers</h2>
        <div class="escaped-content">
            \# This is an escaped H1 header
            \## This is an escaped H2 header
        </div>
    </div>

    <div class="test-section">
        <h2>Test 3: Your Content Style (\\n escapes)</h2>
        <div class="escaped-content">
            # Hello World\\n\\nThis is **bold** and *italic* text.\\n\\n- Item 1\\n- Item 2
        </div>
    </div>

    <div class="test-section">
        <h2>Test 4: Pre Tag Content (now supported)</h2>
        <pre class="test-content">\*This should be bold\* and \_this should be italic\_</pre>
    </div>

    <div class="test-section">
        <h2>Test 5: Complex Content</h2>
        <pre class="test-content">## Features\\n\\n1. **Auto-detection**\\n2. **Multi-element processing**\\n3. **Better escape handling**\\n\\n> This should work now!</pre>
    </div>

    <!-- Load marked.js -->
    <script src="lib/marked.min.js"></script>
    
    <!-- Load our enhanced content script -->
    <script>
        // Enhanced version of content.js for standalone testing
        (function() {
            'use strict';

            // Configuration
            const CONFIG = {
                minTextLength: 10,
                maxTextLength: 10000,
                debounceDelay: 500,
                processPre: true,  // Allow processing <pre> tags
                processCode: false, // Skip <code> tags
                markdownPatterns: [
                    /\\[\*_`#\[\]\\]/g,  // Escaped markdown characters
                    /\\\*[^*]+\\\*/g,    // Escaped bold/italic
                    /\\_[^_]+\\_/g,      // Escaped underscores
                    /\\`[^`]+\\`/g,      // Escaped code
                    /\\#{1,6}\s/g,       // Escaped headers
                    /\\\[[^\]]*\\\]/g,   // Escaped links/references
                    /\\n/g,              // Escaped newlines
                    /\\t/g,              // Escaped tabs
                    /\\r/g               // Escaped carriage returns
                ]
            };

            // Track processed elements to avoid duplication
            let processedElements = new Set(); // Use Set instead of WeakSet so we can clear it
            let isEnabled = true; // Default to enabled
            let debugMode = true; // Enable debug logging
            let processedCount = 0;

            // Utility functions
            function unescapeMarkdown(text) {
                return text
                    .replace(/\\(\*)/g, '$1')
                    .replace(/\\(_)/g, '$1')
                    .replace(/\\(`)/g, '$1')
                    .replace(/\\(#)/g, '$1')
                    .replace(/\\(\[)/g, '$1')
                    .replace(/\\(\])/g, '$1')
                    .replace(/\\(\\)/g, '$1')
                    .replace(/\\n/g, '\n')     // Convert \\n to actual newlines
                    .replace(/\\t/g, '\t')     // Convert \\t to actual tabs
                    .replace(/\\r/g, '\r');    // Convert \\r to actual carriage returns
            }

            function hasEscapedMarkdown(text) {
                return CONFIG.markdownPatterns.some(pattern => pattern.test(text));
            }

            function isValidTextNode(node) {
                if (!node || node.nodeType !== Node.TEXT_NODE) return false;
                
                const text = node.textContent.trim();
                if (text.length < CONFIG.minTextLength || text.length > CONFIG.maxTextLength) return false;
                
                // Skip if parent is script, style, or already processed
                const parent = node.parentElement;
                if (!parent) return false;
                
                const tagName = parent.tagName.toLowerCase();
                
                // Always skip script and style
                if (['script', 'style'].includes(tagName)) return false;
                
                // Conditionally skip code and pre based on config
                if (!CONFIG.processCode && tagName === 'code') return false;
                if (!CONFIG.processPre && tagName === 'pre') return false;
                
                return hasEscapedMarkdown(text);
            }

            function createMarkdownRenderer(originalElement, unescapedText) {
                const container = document.createElement('div');
                container.className = 'markdown-unescape-container';
                
                const header = document.createElement('div');
                header.className = 'markdown-unescape-header';
                header.innerHTML = '📝 Rendered Markdown';
                
                const content = document.createElement('div');
                content.className = 'markdown-unescape-content';
                
                try {
                    // Use marked.js to render markdown
                    if (typeof marked !== 'undefined') {
                        content.innerHTML = marked.parse(unescapedText);
                    } else {
                        // Fallback simple rendering
                        content.innerHTML = unescapedText
                            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                            .replace(/\*(.*?)\*/g, '<em>$1</em>')
                            .replace(/`(.*?)`/g, '<code>$1</code>')
                            .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                            .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                            .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                            .replace(/\n/g, '<br>');
                    }
                } catch (error) {
                    console.error('Markdown rendering error:', error);
                    content.textContent = unescapedText;
                }
                
                container.appendChild(header);
                container.appendChild(content);
                
                return container;
            }

            function removeAllMarkdownContainers() {
                const containers = document.querySelectorAll('.markdown-unescape-container');
                console.log(`🗑️ Removing ${containers.length} markdown containers`);

                containers.forEach(container => {
                    container.remove();
                });

                // Clear the processed elements set so content can be reprocessed when re-enabled
                processedElements.clear();
                processedCount = 0;

                console.log('✅ All markdown containers removed');
                updateStatus();
            }

            function processTextNode(textNode) {
                const parent = textNode.parentElement;
                if (processedElements.has(parent)) return;

                const originalText = textNode.textContent;
                const unescapedText = unescapeMarkdown(originalText);

                console.log(`🔄 Processing text: "${originalText.substring(0, 50)}..."`);
                console.log(`🔄 Unescaped to: "${unescapedText.substring(0, 50)}..."`);

                if (originalText === unescapedText) {
                    console.log('⏭️ No changes needed, skipping');
                    return; // No changes needed
                }

                const renderer = createMarkdownRenderer(parent, unescapedText);

                // Insert after the parent element
                parent.parentNode.insertBefore(renderer, parent.nextSibling);
                processedElements.add(parent);
                processedCount++;

                console.log('✅ Created markdown container');
                updateStatus();
            }

            function scanForMarkdown() {
                if (!isEnabled) {
                    removeAllMarkdownContainers();
                    return;
                }

                console.log('🔍 Scanning for markdown content...');
                
                const walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    {
                        acceptNode: function(node) {
                            const isValid = isValidTextNode(node);
                            if (debugMode && node.textContent.trim().length > 10) {
                                console.log(`📝 Text node check: "${node.textContent.substring(0, 50)}..." - Valid: ${isValid}`);
                                if (node.parentElement) {
                                    console.log(`   Parent: <${node.parentElement.tagName.toLowerCase()}>`);
                                }
                            }
                            return isValid ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
                        }
                    }
                );

                const textNodes = [];
                let node;
                while (node = walker.nextNode()) {
                    textNodes.push(node);
                }

                console.log(`📊 Found ${textNodes.length} valid text nodes to process`);
                textNodes.forEach(processTextNode);
                
                const containers = document.querySelectorAll('.markdown-unescape-container');
                console.log(`✅ Created ${containers.length} markdown containers`);
                
                updateStatus();
            }

            function updateStatus() {
                const containers = document.querySelectorAll('.markdown-unescape-container');
                const statusElement = document.getElementById('processing-status');
                const countElement = document.getElementById('container-count');
                const updateElement = document.getElementById('last-update');
                const toggleBtn = document.getElementById('toggle-btn');

                if (statusElement) {
                    if (!isEnabled) {
                        statusElement.textContent = 'Disabled ❌';
                    } else {
                        statusElement.textContent = containers.length > 0 ? 'Working ✅' : 'No content processed ❌';
                    }
                }
                if (countElement) {
                    countElement.textContent = containers.length;
                }
                if (updateElement) {
                    updateElement.textContent = new Date().toLocaleTimeString();
                }
                if (toggleBtn) {
                    toggleBtn.textContent = isEnabled ? 'Disable Extension' : 'Enable Extension';
                }
            }

            // Global function for toggling extension
            window.toggleExtension = function() {
                isEnabled = !isEnabled;
                console.log(`🔄 Extension ${isEnabled ? 'enabled' : 'disabled'}`);

                // Immediately apply the change
                if (isEnabled) {
                    scanForMarkdown();
                } else {
                    removeAllMarkdownContainers();
                }

                updateStatus();
            };

            // Debounced scan function
            let scanTimeout;
            function debouncedScan() {
                clearTimeout(scanTimeout);
                scanTimeout = setTimeout(scanForMarkdown, CONFIG.debounceDelay);
            }

            // Initialize
            function init() {
                console.log('🚀 Initializing Enhanced Markdown Unescape functionality...');
                
                // Initial scan
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', scanForMarkdown);
                } else {
                    scanForMarkdown();
                }

                // Watch for dynamic content
                const observer = new MutationObserver(debouncedScan);
                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    characterData: true
                });
                
                console.log('✅ Enhanced Markdown Unescape initialized successfully');
                updateStatus();
            }

            // Start the functionality
            init();
        })();
    </script>
</body>
</html>
