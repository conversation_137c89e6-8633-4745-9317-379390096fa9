<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Extension Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        .escaped-content {
            background: #fff3e0;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #ff9800;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>🔧 Debug Extension Test</h1>
    
    <div class="debug-info">
        <strong>Extension Status:</strong> <span id="extension-status">Checking...</span><br>
        <strong>Containers Found:</strong> <span id="container-count">0</span><br>
        <strong>Last Check:</strong> <span id="last-check">Never</span>
    </div>

    <div class="test-section">
        <h2>Test 1: Basic Markdown Escapes (should work)</h2>
        <div class="escaped-content">
            This text contains \*bold text\* and \_italic text\_ and \`inline code\`.
        </div>
    </div>

    <div class="test-section">
        <h2>Test 2: Headers (should work)</h2>
        <div class="escaped-content">
            \# This is an escaped H1 header
            \## This is an escaped H2 header
        </div>
    </div>

    <div class="test-section">
        <h2>Test 3: Your Content Style (currently not supported)</h2>
        <div class="escaped-content">
            # Hello World\\n\\nThis is **bold** and *italic* text.\\n\\n- Item 1\\n- Item 2
        </div>
    </div>

    <div class="test-section">
        <h2>Test 4: Pre Tag Content (currently skipped)</h2>
        <pre>\*This should be bold\* but is in a pre tag</pre>
    </div>

    <div class="test-section">
        <h2>Test 5: Mixed Content</h2>
        <p>Normal text with \*escaped bold\* and some \`escaped code\`.</p>
    </div>

    <script>
        let checkCount = 0;
        
        function updateDebugInfo() {
            checkCount++;
            const containers = document.querySelectorAll('.markdown-unescape-container');
            const status = containers.length > 0 ? 'Working ✅' : 'Not detecting content ❌';
            
            document.getElementById('extension-status').textContent = status;
            document.getElementById('container-count').textContent = containers.length;
            document.getElementById('last-check').textContent = new Date().toLocaleTimeString();
            
            console.log(`Debug check ${checkCount}: Found ${containers.length} containers`);
            
            // Log details about each container
            containers.forEach((container, index) => {
                console.log(`Container ${index + 1}:`, container);
            });
        }
        
        // Check immediately and then every 2 seconds
        updateDebugInfo();
        setInterval(updateDebugInfo, 2000);
        
        // Also check after page load
        window.addEventListener('load', () => {
            setTimeout(updateDebugInfo, 1000);
        });
        
        console.log('Debug test page loaded. Extension should process escaped markdown content.');
        console.log('If you see no containers after a few seconds, the extension may not be loaded correctly.');
    </script>
</body>
</html>
