<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#fff" stroke-width="2"/>
  
  <!-- Tool icon -->
  <g transform="translate(64,64)">
    <!-- Wrench/tool shape -->
    <path d="M-20,-15 L-15,-20 L-10,-15 L-5,-20 L0,-15 L5,-10 L10,-5 L15,0 L10,5 L5,10 L0,15 L-5,10 L-10,5 L-15,0 L-20,-5 Z" 
          fill="#fff" opacity="0.9"/>
    
    <!-- Inner detail -->
    <circle cx="0" cy="0" r="8" fill="none" stroke="#fff" stroke-width="2"/>
    <text x="0" y="6" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">U</text>
  </g>
</svg>
