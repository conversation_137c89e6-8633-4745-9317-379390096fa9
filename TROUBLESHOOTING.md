# 🔧 故障排除指南

## 插件安装检查

### 1. 确认插件已正确安装

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 确保右上角的"开发者模式"已开启
4. 查找"Markdown Unescape Viewer"插件
5. 确保插件状态为"已启用"

### 2. 插件默认状态

- ✅ 插件默认为**启用状态**，安装后会自动开始工作
- 🎛️ 点击浏览器工具栏中的插件图标可以开启/关闭功能
- 🔄 禁用后会**自动移除**所有已渲染的内容
- ⚡ 重新启用后会**立即重新处理**页面内容

### 3. 检查插件文件

确保以下文件都存在且正确：
- ✅ `manifest.json`
- ✅ `content.js`
- ✅ `styles.css`
- ✅ `popup.html`
- ✅ `popup.js`
- ✅ `lib/marked.min.js`
- ✅ `icons/` 目录及图标文件

### 4. 重新加载插件

如果修改了代码：
1. 在 `chrome://extensions/` 页面
2. 找到插件，点击"重新加载"按钮 🔄
3. 刷新测试页面

## 功能测试

### 使用 debug-test.html 页面

1. 打开 `debug-test.html` 文件
2. 查看页面顶部的调试信息
3. 应该看到：
   - Extension Status: Working ✅
   - Containers Found: > 0
   - 在原内容下方出现渲染的 Markdown

### 检查浏览器控制台

1. 按 F12 打开开发者工具
2. 切换到"Console"标签
3. 刷新页面
4. 查找以下日志：
   ```
   🔍 Scanning for markdown content...
   📝 Text node check: "..." - Valid: true/false
   📊 Found X valid text nodes to process
   🔄 Processing text: "..."
   ✅ Created markdown container
   ```

## 常见问题

### ❌ 问题1：看不到任何效果

**可能原因：**
- 插件未正确安装
- 插件被禁用了（检查弹出界面状态）
- 测试内容不符合检测模式
- 内容在被跳过的标签中

**解决方案：**
1. 点击插件图标，确保状态为"Extension is enabled"
2. 使用 `debug-test.html` 或 `standalone-debug.html` 测试
3. 检查控制台日志
4. 确认内容包含转义的 Markdown 字符

### ❌ 问题2：控制台显示错误

**常见错误：**
```
Failed to load resource: lib/marked.min.js
```

**解决方案：**
1. 确认 `lib/marked.min.js` 文件存在
2. 检查文件路径是否正确
3. 重新下载 marked.js 库

### ❌ 问题3：只处理部分内容

**可能原因：**
- 内容在 `<pre>` 或 `<code>` 标签中
- 内容长度不符合要求
- 已经被处理过

**解决方案：**
1. 检查 `CONFIG.processPre` 设置
2. 调整 `CONFIG.minTextLength` 和 `CONFIG.maxTextLength`
3. 刷新页面重新处理

## 支持的内容格式

### ✅ 当前支持的转义字符

- `\*bold\*` → **bold**
- `\_italic\_` → _italic_
- `\`code\`` → `code`
- `\# header` → # header
- `\[link\](url)` → [link](url)
- `\\n` → 换行
- `\\t` → 制表符

### ❌ 当前不支持的格式

- 已经渲染的 Markdown（如 `**bold**`）
- 复杂的嵌套结构
- 某些特殊字符组合

## 配置选项

在 `content.js` 中可以修改以下配置：

```javascript
const CONFIG = {
    minTextLength: 10,        // 最小文本长度
    maxTextLength: 10000,     // 最大文本长度
    debounceDelay: 500,       // 处理延迟（毫秒）
    processPre: true,         // 是否处理 <pre> 标签
    processCode: false,       // 是否处理 <code> 标签
};
```

## 调试步骤

### 1. 基础检查
```javascript
// 在控制台运行
console.log('Extension loaded:', !!window.chrome?.runtime);
console.log('Containers found:', document.querySelectorAll('.markdown-unescape-container').length);
```

### 2. 手动触发扫描
```javascript
// 如果插件有全局函数，可以手动触发
if (window.scanForMarkdown) {
    window.scanForMarkdown();
}
```

### 3. 检查文本节点
```javascript
// 查看页面中的文本节点
const walker = document.createTreeWalker(
    document.body,
    NodeFilter.SHOW_TEXT
);
let node;
while (node = walker.nextNode()) {
    if (node.textContent.includes('\\')) {
        console.log('Found escaped text:', node.textContent.substring(0, 50));
    }
}
```

## 获取帮助

如果问题仍然存在：

1. **检查文件完整性**：确保所有文件都已正确下载
2. **查看控制台**：记录所有错误信息
3. **测试不同内容**：尝试不同类型的转义内容
4. **重新安装**：删除插件后重新安装

## 测试用例

### 应该工作的内容：
```
这段文字包含 \*粗体\* 和 \_斜体\_ 以及 \`代码\`。
\# 这是一个转义的标题
\## 这是二级标题
```

### 不会被处理的内容：
```
这段文字包含 **粗体** 和 *斜体*（已经是正常 Markdown）
```

### 在 <pre> 标签中的内容：
```html
<pre>\*这应该被处理\*（如果 processPre: true）</pre>
```
