// Popup script for Markdown Unescape Viewer
document.addEventListener('DOMContentLoaded', function() {
    const toggleSwitch = document.getElementById('toggleSwitch');
    const statusElement = document.getElementById('status');
    const reloadHint = document.getElementById('reloadHint');

    let currentTabId = null;
    let extensionEnabled = true; // Default state

    // Check if current page supports the extension
    function isPageSupported(url) {
        if (!url) return false;

        // Pages where content scripts cannot run
        const unsupportedPatterns = [
            /^chrome:\/\//,
            /^chrome-extension:\/\//,
            /^moz-extension:\/\//,
            /^edge:\/\//,
            /^about:/,
            /^file:\/\/.*\.pdf$/
        ];

        return !unsupportedPatterns.some(pattern => pattern.test(url));
    }

    // Get current tab and check extension status
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        const activeTab = tabs[0];
        currentTabId = activeTab.id;

        // Check if page is supported
        if (!isPageSupported(activeTab.url)) {
            statusElement.textContent = 'Cannot run on this page type';
            statusElement.className = 'status disabled';
            toggleSwitch.style.opacity = '0.5';
            toggleSwitch.style.pointerEvents = 'none';
            return;
        }

        // Try to get status from storage first
        chrome.storage.local.get(['extensionEnabled'], function(result) {
            extensionEnabled = result.extensionEnabled !== false; // Default to true

            // Try to communicate with content script
            chrome.tabs.sendMessage(activeTab.id, {action: 'getStatus'}, function(response) {
                if (chrome.runtime.lastError) {
                    // Content script not ready, show stored state
                    statusElement.textContent = 'Extension will activate when page loads';
                    statusElement.className = 'status';
                    updateUI(extensionEnabled);
                    return;
                }

                const isEnabled = response && response.enabled;
                extensionEnabled = isEnabled;
                updateUI(isEnabled);
            });
        });
    });

    // Handle toggle switch click
    toggleSwitch.addEventListener('click', function() {
        if (!currentTabId) return;

        chrome.tabs.get(currentTabId, function(tab) {
            if (!isPageSupported(tab.url)) {
                statusElement.textContent = 'Cannot toggle on this page';
                statusElement.className = 'status disabled';
                return;
            }

            chrome.tabs.sendMessage(currentTabId, {action: 'toggle'}, function(response) {
                if (chrome.runtime.lastError) {
                    // Content script not available, store preference and show message
                    extensionEnabled = !extensionEnabled;
                    chrome.storage.local.set({extensionEnabled: extensionEnabled});

                    statusElement.textContent = extensionEnabled ?
                        'Will enable when page reloads' :
                        'Will disable when page reloads';
                    statusElement.className = 'status warning';
                    reloadHint.style.display = 'block';
                    updateUI(extensionEnabled);
                    return;
                }

                const isEnabled = response && response.enabled;
                extensionEnabled = isEnabled;

                // Store the new state
                chrome.storage.local.set({extensionEnabled: isEnabled});
                reloadHint.style.display = 'none'; // Hide reload hint when working normally
                updateUI(isEnabled);
            });
        });
    });

    function updateUI(isEnabled) {
        if (isEnabled) {
            toggleSwitch.classList.add('active');
            if (statusElement.textContent.includes('Cannot') ||
                statusElement.textContent.includes('will') ||
                statusElement.textContent.includes('Will')) {
                // Don't override special messages
            } else {
                statusElement.textContent = 'Extension is enabled';
                statusElement.className = 'status enabled';
            }
        } else {
            toggleSwitch.classList.remove('active');
            if (statusElement.textContent.includes('Cannot') ||
                statusElement.textContent.includes('will') ||
                statusElement.textContent.includes('Will')) {
                // Don't override special messages
            } else {
                statusElement.textContent = 'Extension is disabled';
                statusElement.className = 'status disabled';
            }
        }
    }
});
