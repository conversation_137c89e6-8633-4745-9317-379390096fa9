// Popup script for Markdown Unescape Viewer
document.addEventListener('DOMContentLoaded', function() {
    const toggleSwitch = document.getElementById('toggleSwitch');
    const statusElement = document.getElementById('status');
    
    // Get current tab and check extension status
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        const activeTab = tabs[0];
        
        // Send message to content script to get current status
        chrome.tabs.sendMessage(activeTab.id, {action: 'getStatus'}, function(response) {
            if (chrome.runtime.lastError) {
                statusElement.textContent = 'Extension not active on this page';
                statusElement.className = 'status disabled';
                return;
            }
            
            const isEnabled = response && response.enabled;
            updateUI(isEnabled);
        });
    });
    
    // Handle toggle switch click
    toggleSwitch.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            const activeTab = tabs[0];
            
            chrome.tabs.sendMessage(activeTab.id, {action: 'toggle'}, function(response) {
                if (chrome.runtime.lastError) {
                    statusElement.textContent = 'Cannot toggle on this page';
                    statusElement.className = 'status disabled';
                    return;
                }
                
                const isEnabled = response && response.enabled;
                updateUI(isEnabled);
            });
        });
    });
    
    function updateUI(isEnabled) {
        if (isEnabled) {
            toggleSwitch.classList.add('active');
            statusElement.textContent = 'Extension is enabled';
            statusElement.className = 'status enabled';
        } else {
            toggleSwitch.classList.remove('active');
            statusElement.textContent = 'Extension is disabled';
            statusElement.className = 'status disabled';
        }
    }
});
