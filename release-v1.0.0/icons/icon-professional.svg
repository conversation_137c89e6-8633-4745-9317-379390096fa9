<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4285f4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a73e8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e8f0fe;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle with shadow -->
  <circle cx="64" cy="64" r="56" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Inner circle for depth -->
  <circle cx="64" cy="64" r="52" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
  
  <!-- Markdown "M" symbol -->
  <text x="64" y="85" font-family="Arial, sans-serif" font-size="42" font-weight="bold" 
        text-anchor="middle" fill="url(#textGradient)">M</text>
  
  <!-- Escape backslash -->
  <text x="88" y="45" font-family="Arial, sans-serif" font-size="18" font-weight="bold" 
        text-anchor="middle" fill="#ffd700" opacity="0.9">\</text>
  
  <!-- Arrow indicating transformation -->
  <g stroke="#ffd700" stroke-width="2" fill="none" opacity="0.8">
    <path d="M 32 38 L 42 38"/>
    <path d="M 38 34 L 42 38 L 38 42"/>
  </g>
  
  <!-- Small decorative elements -->
  <circle cx="45" cy="25" r="2" fill="rgba(255,255,255,0.6)"/>
  <circle cx="83" cy="25" r="1.5" fill="rgba(255,255,255,0.4)"/>
  <circle cx="25" cy="83" r="1.5" fill="rgba(255,255,255,0.4)"/>
</svg>
