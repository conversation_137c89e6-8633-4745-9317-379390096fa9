// Markdown Unescape Viewer - Content Script
(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        minTextLength: 10,
        maxTextLength: 10000,
        debounceDelay: 500,
        markdownPatterns: [
            /\\[\*_`#\[\]\\]/g,  // Escaped markdown characters
            /\\\*[^*]+\\\*/g,    // Escaped bold/italic
            /\\_[^_]+\\_/g,      // Escaped underscores
            /\\`[^`]+\\`/g,      // Escaped code
            /\\#{1,6}\s/g,       // Escaped headers
            /\\\[[^\]]*\\\]/g    // Escaped links/references
        ]
    };

    // Track processed elements to avoid duplication
    const processedElements = new WeakSet();
    let isEnabled = true;

    // Utility functions
    function unescapeMarkdown(text) {
        return text
            .replace(/\\(\*)/g, '$1')
            .replace(/\\(_)/g, '$1')
            .replace(/\\(`)/g, '$1')
            .replace(/\\(#)/g, '$1')
            .replace(/\\(\[)/g, '$1')
            .replace(/\\(\])/g, '$1')
            .replace(/\\(\\)/g, '$1');
    }

    function hasEscapedMarkdown(text) {
        return CONFIG.markdownPatterns.some(pattern => pattern.test(text));
    }

    function isValidTextNode(node) {
        if (!node || node.nodeType !== Node.TEXT_NODE) return false;
        
        const text = node.textContent.trim();
        if (text.length < CONFIG.minTextLength || text.length > CONFIG.maxTextLength) return false;
        
        // Skip if parent is script, style, or already processed
        const parent = node.parentElement;
        if (!parent) return false;
        
        const tagName = parent.tagName.toLowerCase();
        if (['script', 'style', 'code', 'pre'].includes(tagName)) return false;
        
        return hasEscapedMarkdown(text);
    }

    function createMarkdownRenderer(originalElement, unescapedText) {
        const container = document.createElement('div');
        container.className = 'markdown-unescape-container';
        
        const header = document.createElement('div');
        header.className = 'markdown-unescape-header';
        header.innerHTML = '📝 Rendered Markdown';
        
        const content = document.createElement('div');
        content.className = 'markdown-unescape-content';
        
        try {
            // Use marked.js to render markdown
            if (typeof marked !== 'undefined') {
                content.innerHTML = marked.parse(unescapedText);
            } else {
                // Fallback simple rendering
                content.innerHTML = unescapedText
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/`(.*?)`/g, '<code>$1</code>')
                    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                    .replace(/\n/g, '<br>');
            }
        } catch (error) {
            console.error('Markdown rendering error:', error);
            content.textContent = unescapedText;
        }
        
        container.appendChild(header);
        container.appendChild(content);
        
        return container;
    }

    function processTextNode(textNode) {
        if (processedElements.has(textNode.parentElement)) return;
        
        const originalText = textNode.textContent;
        const unescapedText = unescapeMarkdown(originalText);
        
        if (originalText === unescapedText) return; // No changes needed
        
        const parent = textNode.parentElement;
        const renderer = createMarkdownRenderer(parent, unescapedText);
        
        // Insert after the parent element
        parent.parentNode.insertBefore(renderer, parent.nextSibling);
        processedElements.add(parent);
    }

    function scanForMarkdown() {
        if (!isEnabled) return;
        
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: function(node) {
                    return isValidTextNode(node) ? 
                        NodeFilter.FILTER_ACCEPT : 
                        NodeFilter.FILTER_REJECT;
                }
            }
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        textNodes.forEach(processTextNode);
    }

    // Debounced scan function
    let scanTimeout;
    function debouncedScan() {
        clearTimeout(scanTimeout);
        scanTimeout = setTimeout(scanForMarkdown, CONFIG.debounceDelay);
    }

    // Initialize
    function init() {
        // Initial scan
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', scanForMarkdown);
        } else {
            scanForMarkdown();
        }

        // Watch for dynamic content
        const observer = new MutationObserver(debouncedScan);
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });

        // Listen for messages from popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'toggle') {
                isEnabled = !isEnabled;
                sendResponse({enabled: isEnabled});
            } else if (request.action === 'getStatus') {
                sendResponse({enabled: isEnabled});
            }
        });
    }

    // Start the extension
    init();
})();
