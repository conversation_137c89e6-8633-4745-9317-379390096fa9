// Markdown Unescape Viewer - Content Script
(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        minTextLength: 10,
        maxTextLength: 10000,
        debounceDelay: 500,
        processPre: true,  // Allow processing <pre> tags
        processCode: false, // Skip <code> tags
        markdownPatterns: [
            /\\[\*_`#\[\]\\]/g,  // Escaped markdown characters
            /\\\*[^*]+\\\*/g,    // Escaped bold/italic
            /\\_[^_]+\\_/g,      // Escaped underscores
            /\\`[^`]+\\`/g,      // Escaped code
            /\\#{1,6}\s/g,       // Escaped headers
            /\\\[[^\]]*\\\]/g,   // Escaped links/references
            /\\n/g,              // Escaped newlines
            /\\t/g,              // Escaped tabs
            /\\r/g               // Escaped carriage returns
        ]
    };

    // Track processed elements to avoid duplication
    let processedElements = new Set(); // Use Set instead of WeakSet so we can clear it
    let isEnabled = true; // Default to enabled
    let debugMode = true; // Enable debug logging
    let isInitialized = false;

    // Utility functions
    function unescapeMarkdown(text) {
        return text
            .replace(/\\(\*)/g, '$1')
            .replace(/\\(_)/g, '$1')
            .replace(/\\(`)/g, '$1')
            .replace(/\\(#)/g, '$1')
            .replace(/\\(\[)/g, '$1')
            .replace(/\\(\])/g, '$1')
            .replace(/\\(\\)/g, '$1')
            .replace(/\\n/g, '\n')     // Convert \\n to actual newlines
            .replace(/\\t/g, '\t')     // Convert \\t to actual tabs
            .replace(/\\r/g, '\r');    // Convert \\r to actual carriage returns
    }

    function hasEscapedMarkdown(text) {
        return CONFIG.markdownPatterns.some(pattern => pattern.test(text));
    }

    function isValidTextNode(node) {
        if (!node || node.nodeType !== Node.TEXT_NODE) return false;

        const text = node.textContent.trim();
        if (text.length < CONFIG.minTextLength || text.length > CONFIG.maxTextLength) return false;

        // Skip if parent is script, style, or already processed
        const parent = node.parentElement;
        if (!parent) return false;

        const tagName = parent.tagName.toLowerCase();

        // Always skip script and style
        if (['script', 'style'].includes(tagName)) return false;

        // Conditionally skip code and pre based on config
        if (!CONFIG.processCode && tagName === 'code') return false;
        if (!CONFIG.processPre && tagName === 'pre') return false;

        return hasEscapedMarkdown(text);
    }

    function createMarkdownRenderer(originalElement, unescapedText) {
        const container = document.createElement('div');
        container.className = 'markdown-unescape-container';
        
        const header = document.createElement('div');
        header.className = 'markdown-unescape-header';
        header.innerHTML = '📝 Rendered Content';
        
        const content = document.createElement('div');
        content.className = 'markdown-unescape-content';
        
        try {
            // Use marked.js to render markdown
            if (typeof marked !== 'undefined') {
                content.innerHTML = marked.parse(unescapedText);
            } else {
                // Fallback simple rendering
                content.innerHTML = unescapedText
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/`(.*?)`/g, '<code>$1</code>')
                    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                    .replace(/\n/g, '<br>');
            }
        } catch (error) {
            console.error('Markdown rendering error:', error);
            content.textContent = unescapedText;
        }
        
        container.appendChild(header);
        container.appendChild(content);
        
        return container;
    }

    function processTextNode(textNode) {
        const parent = textNode.parentElement;
        if (processedElements.has(parent)) return;

        const originalText = textNode.textContent;
        const unescapedText = unescapeMarkdown(originalText);

        console.log(`🔄 Processing text: "${originalText.substring(0, 50)}..."`);
        console.log(`🔄 Unescaped to: "${unescapedText.substring(0, 50)}..."`);

        if (originalText === unescapedText) {
            console.log('⏭️ No changes needed, skipping');
            return; // No changes needed
        }

        const renderer = createMarkdownRenderer(parent, unescapedText);

        // Insert after the parent element
        parent.parentNode.insertBefore(renderer, parent.nextSibling);
        processedElements.add(parent);

        console.log('✅ Created markdown container');
    }

    function removeAllMarkdownContainers() {
        const containers = document.querySelectorAll('.markdown-unescape-container');
        console.log(`🗑️ Removing ${containers.length} markdown containers`);

        containers.forEach(container => {
            container.remove();
        });

        // Clear the processed elements set so content can be reprocessed when re-enabled
        processedElements.clear();

        console.log('✅ All markdown containers removed');
    }

    function scanForMarkdown() {
        if (!isEnabled) {
            removeAllMarkdownContainers();
            return;
        }

        console.log('🔍 Scanning for markdown content...');

        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: function(node) {
                    const isValid = isValidTextNode(node);
                    if (debugMode && node.textContent.trim().length > 10) {
                        console.log(`📝 Text node check: "${node.textContent.substring(0, 50)}..." - Valid: ${isValid}`);
                        if (node.parentElement) {
                            console.log(`   Parent: <${node.parentElement.tagName.toLowerCase()}>`);
                        }
                    }
                    return isValid ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
                }
            }
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        console.log(`📊 Found ${textNodes.length} valid text nodes to process`);
        textNodes.forEach(processTextNode);

        const containers = document.querySelectorAll('.markdown-unescape-container');
        console.log(`✅ Created ${containers.length} markdown containers`);
    }

    // Debounced scan function
    let scanTimeout;
    function debouncedScan() {
        clearTimeout(scanTimeout);
        scanTimeout = setTimeout(scanForMarkdown, CONFIG.debounceDelay);
    }

    // Initialize with stored preferences
    function initializeWithStoredState() {
        chrome.storage.local.get(['extensionEnabled'], function(result) {
            isEnabled = result.extensionEnabled !== false; // Default to true
            console.log(`🚀 Initializing Markdown Unescape functionality... (enabled: ${isEnabled})`);

            isInitialized = true;

            // Initial scan if enabled
            if (isEnabled) {
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', scanForMarkdown);
                } else {
                    scanForMarkdown();
                }
            }
        });
    }

    // Initialize
    function init() {
        // Load initial state from storage
        initializeWithStoredState();

        // Watch for dynamic content
        const observer = new MutationObserver(debouncedScan);
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });

        // Listen for messages from popup
        chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
            if (request.action === 'toggle') {
                isEnabled = !isEnabled;
                console.log(`🔄 Extension ${isEnabled ? 'enabled' : 'disabled'}`);

                // Store the new state
                chrome.storage.local.set({extensionEnabled: isEnabled});

                // Immediately apply the change
                if (isEnabled) {
                    scanForMarkdown();
                } else {
                    removeAllMarkdownContainers();
                }

                sendResponse({enabled: isEnabled});
            } else if (request.action === 'getStatus') {
                sendResponse({enabled: isEnabled});
            }
        });

        console.log('✅ Markdown Unescape initialized successfully');
    }

    // Start the extension
    init();
})();
