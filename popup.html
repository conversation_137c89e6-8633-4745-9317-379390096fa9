<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 18px;
            margin: 0 0 5px 0;
            color: #333;
        }
        
        .header p {
            font-size: 12px;
            color: #666;
            margin: 0;
        }
        
        .toggle-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .toggle-label {
            font-weight: 500;
            color: #333;
        }
        
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .toggle-switch.active {
            background: #007bff;
        }
        
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }
        
        .toggle-switch.active::after {
            transform: translateX(26px);
        }
        
        .info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 12px;
            font-size: 12px;
            color: #1565c0;
            line-height: 1.4;
        }
        
        .status {
            text-align: center;
            margin-top: 15px;
            font-size: 12px;
            color: #666;
        }
        
        .status.enabled {
            color: #28a745;
        }
        
        .status.disabled {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📝 Markdown Unescape</h1>
        <p>Render escaped markdown content</p>
    </div>
    
    <div class="toggle-container">
        <span class="toggle-label">Enable Extension</span>
        <div class="toggle-switch" id="toggleSwitch"></div>
    </div>
    
    <div class="info">
        This extension automatically detects escaped markdown content on web pages and displays the rendered version below the original text.
    </div>
    
    <div class="status" id="status">Checking status...</div>
    
    <script src="popup.js"></script>
</body>
</html>
