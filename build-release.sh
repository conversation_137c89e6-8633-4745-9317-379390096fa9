#!/bin/bash

# Chrome Web Store Release Builder
# 为Chrome Web Store创建发布包

echo "🚀 Building Chrome Web Store release package..."

# 设置版本号
VERSION="1.0.0"
RELEASE_DIR="release-v${VERSION}"
ZIP_NAME="markdown-unescape-viewer-v${VERSION}.zip"

# 清理之前的构建
if [ -d "$RELEASE_DIR" ]; then
    echo "🧹 Cleaning previous build..."
    rm -rf "$RELEASE_DIR"
fi

if [ -f "$ZIP_NAME" ]; then
    rm "$ZIP_NAME"
fi

# 创建发布目录
echo "📁 Creating release directory..."
mkdir "$RELEASE_DIR"

# 复制必需文件
echo "📋 Copying essential files..."

# 核心文件
cp manifest.json "$RELEASE_DIR/"
cp content-production.js "$RELEASE_DIR/content.js"  # 使用生产版本
cp popup.html "$RELEASE_DIR/"
cp popup.js "$RELEASE_DIR/"
cp styles.css "$RELEASE_DIR/"

# 库文件
mkdir -p "$RELEASE_DIR/lib"
cp lib/marked.min.js "$RELEASE_DIR/lib/"

# 图标文件
mkdir -p "$RELEASE_DIR/icons"
cp icons/*.png "$RELEASE_DIR/icons/" 2>/dev/null || echo "⚠️  PNG icons not found, please create them"
cp icons/*.svg "$RELEASE_DIR/icons/" 2>/dev/null || echo "ℹ️  SVG icons copied"

# 文档文件
cp PRIVACY_POLICY.md "$RELEASE_DIR/"

# 验证必需文件
echo "🔍 Verifying required files..."
required_files=(
    "manifest.json"
    "content.js"
    "popup.html"
    "popup.js"
    "styles.css"
    "lib/marked.min.js"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -f "$RELEASE_DIR/$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ Missing required files:"
    printf '%s\n' "${missing_files[@]}"
    exit 1
fi

# 检查图标文件
icon_files=(
    "icons/icon16.png"
    "icons/icon48.png"
    "icons/icon128.png"
)

missing_icons=()
for icon in "${icon_files[@]}"; do
    if [ ! -f "$RELEASE_DIR/$icon" ]; then
        missing_icons+=("$icon")
    fi
done

if [ ${#missing_icons[@]} -ne 0 ]; then
    echo "⚠️  Missing icon files (you need to create PNG versions):"
    printf '%s\n' "${missing_icons[@]}"
    echo "ℹ️  You can convert the SVG icons to PNG using online tools or image editors"
fi

# 创建ZIP包
echo "📦 Creating ZIP package..."
cd "$RELEASE_DIR"
zip -r "../$ZIP_NAME" .
cd ..

# 显示文件大小
file_size=$(du -h "$ZIP_NAME" | cut -f1)
echo "✅ Release package created: $ZIP_NAME ($file_size)"

# 显示包内容
echo "📋 Package contents:"
unzip -l "$ZIP_NAME"

# 最终检查
echo ""
echo "🎯 Pre-submission checklist:"
echo "  ✅ Core functionality files included"
echo "  ✅ Production version (no debug logs)"
echo "  ✅ Privacy policy included"
if [ ${#missing_icons[@]} -eq 0 ]; then
    echo "  ✅ All required icons present"
else
    echo "  ⚠️  Some icons missing - create PNG versions before submission"
fi

echo ""
echo "📝 Next steps:"
echo "  1. Create PNG icons if missing (16x16, 48x48, 128x128)"
echo "  2. Take screenshots (1280x800) for the store"
echo "  3. Go to Chrome Web Store Developer Dashboard"
echo "  4. Upload $ZIP_NAME"
echo "  5. Fill in store information"
echo "  6. Submit for review"

echo ""
echo "🔗 Useful links:"
echo "  • Developer Dashboard: https://chrome.google.com/webstore/devconsole/"
echo "  • Icon Generator: https://www.favicon-generator.org/"
echo "  • Screenshot Tool: Built-in browser dev tools"

echo ""
echo "🎉 Build complete! Good luck with your submission!"
