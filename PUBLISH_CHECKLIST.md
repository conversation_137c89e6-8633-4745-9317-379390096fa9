# 🚀 Chrome Web Store 发布检查清单

## 📋 发布前准备

### ✅ 代码准备
- [x] 功能完整且稳定
- [x] 所有已知bug已修复
- [x] 代码已优化和清理
- [x] 移除调试代码和console.log (保留必要的)
- [x] 测试所有主要功能
- [x] 兼容性测试完成

### ✅ 文件准备
- [x] `manifest.json` 完整且正确
- [x] 所有图标文件 (16x16, 48x48, 128x128)
- [x] 隐私政策文档
- [x] README 文档
- [ ] 商店截图 (1280x800)
- [ ] 宣传图片 (可选)

### ✅ 法律文档
- [x] 隐私政策
- [ ] 使用条款 (可选但推荐)
- [ ] 开源许可证 (如适用)

## 🏪 Chrome Web Store 账户设置

### 开发者账户
1. **注册开发者账户**
   - 访问 [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)
   - 使用Google账户登录
   - 支付一次性注册费用 ($5 USD)

2. **验证身份**
   - 提供必要的身份验证信息
   - 等待账户审核通过

## 📦 打包扩展

### 创建发布包
```bash
# 1. 创建干净的发布目录
mkdir release
cp -r . release/
cd release

# 2. 移除不必要的文件
rm -rf .git
rm -rf node_modules
rm -rf tests
rm -rf store-assets
rm -f *.md (除了必要的)
rm -f test-*.html
rm -f standalone-*.html
rm -f simple-server.py
rm -f playwright.config.js
rm -f package.json

# 3. 保留的文件
# - manifest.json
# - content.js
# - popup.html
# - popup.js
# - styles.css
# - lib/marked.min.js
# - icons/ (所有图标)

# 4. 创建ZIP文件
zip -r markdown-unescape-viewer-v1.0.0.zip .
```

## 📸 截图准备

### 必需截图 (1280x800)
1. **主功能展示**
   - 显示转义内容和渲染结果的对比
   
2. **插件界面**
   - 显示popup界面和开关功能
   
3. **多种内容类型**
   - 展示不同类型的markdown渲染
   
4. **使用场景**
   - 在真实网站上的使用效果

### 截图技巧
- 使用高分辨率显示器
- 确保界面清晰可读
- 添加适当的标注和说明
- 保持一致的视觉风格

## 📝 商店信息填写

### 基本信息
- **名称**: Markdown Unescape Viewer
- **简短描述**: Automatically detects and renders escaped markdown content
- **详细描述**: [使用 store-assets/README.md 中的内容]
- **类别**: Developer Tools
- **语言**: English (主要) + 中文 (可选)

### 权限说明
为每个权限提供清晰的解释：
- **activeTab**: "Access current tab to detect escaped markdown content"
- **storage**: "Save your enable/disable preference locally"

### 支持信息
- **支持网站**: GitHub repository URL
- **支持邮箱**: 您的联系邮箱
- **隐私政策**: 上传隐私政策文档

## 🔍 审核准备

### 常见审核要点
1. **功能描述准确**
   - 确保描述与实际功能一致
   
2. **权限使用合理**
   - 只请求必要的权限
   - 为每个权限提供说明
   
3. **隐私政策完整**
   - 清楚说明数据处理方式
   - 符合相关法规要求
   
4. **用户体验良好**
   - 界面友好易用
   - 功能稳定可靠

### 可能的审核问题
- 权限过多或不必要
- 功能描述不清楚
- 隐私政策缺失或不完整
- 截图质量不佳
- 代码质量问题

## 📅 发布流程

### 提交步骤
1. **上传ZIP包**
2. **填写商店信息**
3. **上传截图和图标**
4. **设置定价** (建议免费)
5. **选择发布地区** (全球)
6. **提交审核**

### 审核时间
- 通常需要 **1-3个工作日**
- 首次发布可能需要更长时间
- 节假日期间可能延长

### 审核结果
- **通过**: 扩展自动发布到商店
- **拒绝**: 收到详细的拒绝原因，修改后重新提交

## 🎯 发布后

### 监控和维护
- 定期检查用户评价和反馈
- 及时修复发现的问题
- 根据用户需求更新功能
- 保持与Chrome浏览器的兼容性

### 推广策略
- 在相关技术社区分享
- 撰写技术博客介绍
- 收集用户反馈改进产品
- 考虑添加更多语言支持

## 📞 支持资源

- [Chrome Web Store Developer Documentation](https://developer.chrome.com/docs/webstore/)
- [Chrome Extension Development Guide](https://developer.chrome.com/docs/extensions/)
- [Chrome Web Store Policy](https://developer.chrome.com/docs/webstore/program-policies/)

---

**提示**: 首次发布建议先在小范围内测试，确保一切正常后再大规模推广。
