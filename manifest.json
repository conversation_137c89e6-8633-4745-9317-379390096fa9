{"manifest_version": 3, "name": "Markdown Unescape Viewer", "version": "2.0.0", "description": "Detects escaped markdown content on web pages and displays unescaped, rendered markdown below the original content", "permissions": ["activeTab", "storage"], "content_scripts": [{"matches": ["<all_urls>"], "js": ["lib/marked.min.js", "content.js"], "css": ["styles.css"], "run_at": "document_end"}], "action": {"default_popup": "popup.html", "default_title": "Markdown Unescape Viewer"}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}