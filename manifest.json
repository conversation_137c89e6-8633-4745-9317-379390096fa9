{"manifest_version": 3, "name": "Markdown Unescape Viewer", "version": "1.0.0", "description": "Automatically detects and renders escaped markdown content on web pages for better readability", "short_name": "MD Unescape", "author": "Your Name", "homepage_url": "https://github.com/yourusername/markdown-unescape-viewer", "permissions": ["activeTab", "storage"], "content_scripts": [{"matches": ["<all_urls>"], "js": ["lib/marked.min.js", "content.js"], "css": ["styles.css"], "run_at": "document_end"}], "action": {"default_popup": "popup.html", "default_title": "Markdown Unescape Viewer"}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}