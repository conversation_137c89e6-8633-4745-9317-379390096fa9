# 📖 使用指南

## 🚀 快速开始

### 1. 安装插件

1. **下载项目文件**到本地目录
2. **打开 Chrome 浏览器**，访问 `chrome://extensions/`
3. **开启开发者模式**（右上角开关）
4. **点击"加载已解压的扩展程序"**
5. **选择项目目录**
6. **插件安装完成**！会在工具栏显示图标

### 2. 验证安装

使用测试页面验证功能：
- 打开 `standalone-debug.html` 进行独立测试
- 或者打开 `debug-test.html` 测试Chrome扩展

## 🎛️ 插件控制

### 启用/禁用插件

1. **点击工具栏中的插件图标** 📝
2. **查看弹出窗口状态**：
   - ✅ "Extension is enabled" - 插件已启用
   - ❌ "Extension is disabled" - 插件已禁用
3. **点击开关切换状态**

### 默认行为

- 🟢 **插件默认启用**：安装后自动开始工作
- 🔄 **实时切换**：禁用后立即移除所有渲染内容
- ⚡ **即时生效**：启用后立即重新处理页面内容

## 📝 支持的内容格式

### ✅ 会被处理的内容

```
\*粗体文本\*          → **粗体文本**
\_斜体文本\_          → _斜体文本_
\`行内代码\`          → `行内代码`
\# 一级标题           → # 一级标题
\## 二级标题          → ## 二级标题
\[链接\](url)        → [链接](url)
\\n                  → 换行
\\t                  → 制表符
```

### ✅ 支持的标签

- `<div>` 内的文本 ✅
- `<p>` 内的文本 ✅
- `<pre>` 内的文本 ✅（新增支持）
- `<span>` 内的文本 ✅

### ❌ 不会被处理的内容

- `<script>` 标签内容 ❌
- `<style>` 标签内容 ❌
- `<code>` 标签内容 ❌（可配置）
- 已经渲染的 Markdown（如 `**粗体**`）❌
- 长度小于10字符的文本 ❌

## 🔧 实际使用场景

### 1. 网页内容查看

当您在网页上看到这样的内容：
```
API返回: \*\*成功\*\* 状态码: \`200\`
```

插件会在下方显示：
> **API返回:** **成功** 状态码: `200`

### 2. 代码文档查看

当您看到转义的文档：
```
\# 使用说明\\n\\n1. 安装依赖\\n2. 运行程序
```

插件会渲染为：
> # 使用说明
> 
> 1. 安装依赖
> 2. 运行程序

### 3. JSON/API 响应查看

对于包含转义内容的JSON：
```
{"message": "\\n\\n## 重要提醒\\n\\n请检查 \`config.json\` 文件"}
```

插件会提取并渲染message内容。

## 🎨 渲染效果

### 视觉特征

- 📦 **容器样式**：带边框的卡片式设计
- 📝 **标题栏**：显示"📝 Rendered Markdown"
- 🎨 **美观样式**：支持深色模式
- 📱 **响应式**：适配不同屏幕尺寸

### 动画效果

- ✨ **滑入动画**：内容出现时的平滑过渡
- 🔄 **实时更新**：动态内容自动处理

## 🐛 调试功能

### 控制台日志

插件会输出详细的调试信息：
```
🔍 Scanning for markdown content...
📝 Text node check: "..." - Valid: true/false
📊 Found X valid text nodes to process
🔄 Processing text: "..."
✅ Created markdown container
```

### 独立测试页面

使用 `standalone-debug.html` 进行测试：
- 实时状态显示
- 容器计数
- 启用/禁用按钮
- 详细调试信息

## ⚙️ 高级配置

### 修改配置

在 `content.js` 中可以调整：

```javascript
const CONFIG = {
    minTextLength: 10,        // 最小处理长度
    maxTextLength: 10000,     // 最大处理长度
    debounceDelay: 500,       // 处理延迟
    processPre: true,         // 处理<pre>标签
    processCode: false,       // 处理<code>标签
};
```

### 自定义样式

修改 `styles.css` 来自定义外观：
- 容器颜色和边框
- 字体和大小
- 动画效果
- 深色模式适配

## 📊 性能说明

### 优化特性

- 🚀 **高效扫描**：使用TreeWalker API
- 🔄 **防抖处理**：避免频繁重复处理
- 💾 **智能缓存**：避免重复处理相同内容
- ⚡ **按需处理**：只处理符合条件的内容

### 性能建议

- 对于大型页面，可以增加 `debounceDelay`
- 如果不需要处理 `<pre>` 标签，可以设置 `processPre: false`
- 调整 `maxTextLength` 来限制处理的内容大小

## 🆘 常见问题

### Q: 为什么有些内容没有被处理？

A: 检查以下几点：
1. 内容是否包含转义字符（如 `\*`）
2. 内容长度是否符合要求（≥10字符）
3. 内容是否在被跳过的标签中
4. 插件是否已启用

### Q: 如何处理动态加载的内容？

A: 插件使用 MutationObserver 自动监听页面变化，动态内容会被自动处理。

### Q: 可以自定义渲染样式吗？

A: 可以！修改 `styles.css` 文件中的 `.markdown-unescape-container` 相关样式。

### Q: 插件会影响页面性能吗？

A: 插件经过优化，对性能影响很小。使用防抖和智能缓存机制避免不必要的处理。

## 🔄 更新日志

### v1.1.0 (当前版本)
- ✅ 默认启用状态
- ✅ 禁用时自动移除渲染内容
- ✅ 支持 `<pre>` 标签内容
- ✅ 支持 `\\n`, `\\t`, `\\r` 转义字符
- ✅ 增强调试功能
- ✅ 改进用户体验

### v1.0.0
- ✅ 基础 Markdown 转义检测
- ✅ 基本渲染功能
- ✅ Chrome 扩展支持
