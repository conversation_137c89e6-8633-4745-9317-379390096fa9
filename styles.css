/* Markdown Unescape Viewer Styles */

.markdown-unescape-container {
    margin: 10px 0;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    position: relative;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.markdown-unescape-header {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    gap: 8px;
}

.markdown-unescape-header::before {
    content: '';
    width: 4px;
    height: 16px;
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-radius: 2px;
}

.markdown-unescape-content {
    line-height: 1.6;
    color: #FFFFFF;
}

/* Markdown content styling */
.markdown-unescape-content h1,
.markdown-unescape-content h2,
.markdown-unescape-content h3,
.markdown-unescape-content h4,
.markdown-unescape-content h5,
.markdown-unescape-content h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    line-height: 1.25;
}

.markdown-unescape-content h1 {
    font-size: 1.8em;
    color: #FFFFFF;
    border-bottom: 2px solid #e1e5e9;
    padding-bottom: 8px;
}

.markdown-unescape-content h2 {
    font-size: 1.5em;
    color: #FFFFFF;
}

.markdown-unescape-content h3 {
    font-size: 1.3em;
    color: #FFFFFF;
}

.markdown-unescape-content p {
    margin: 8px 0;
}

.markdown-unescape-content strong {
    font-weight: 700;
    color: #FFFFFF;
}

.markdown-unescape-content em {
    font-style: italic;
    color: #FFFFFF;
}

.markdown-unescape-content code {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 2px 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: #e83e8c;
}

.markdown-unescape-content pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    overflow-x: auto;
    margin: 12px 0;
}

.markdown-unescape-content pre code {
    background: none;
    border: none;
    padding: 0;
    color: #FFFFFF;
}

.markdown-unescape-content blockquote {
    border-left: 4px solid #007bff;
    margin: 12px 0;
    padding: 8px 16px;
    background: #f8f9fa;
    color: #FFFFFF;
    font-style: italic;
}

.markdown-unescape-content ul,
.markdown-unescape-content ol {
    margin: 8px 0;
    padding-left: 24px;
}

.markdown-unescape-content li {
    margin: 4px 0;
}

.markdown-unescape-content a {
    color: #007bff;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.2s ease;
}

.markdown-unescape-content a:hover {
    border-bottom-color: #007bff;
}

.markdown-unescape-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 12px 0;
}

.markdown-unescape-content th,
.markdown-unescape-content td {
    border: 1px solid #dee2e6;
    padding: 8px 12px;
    text-align: left;
}

.markdown-unescape-content th {
    background: #495057;
    color: #FFFFFF;
    font-weight: 600;
}

.markdown-unescape-content hr {
    border: none;
    height: 2px;
    background: linear-gradient(90deg, transparent, #dee2e6, transparent);
    margin: 20px 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .markdown-unescape-container {
        margin: 8px 0;
        padding: 12px;
    }
    
    .markdown-unescape-content h1 {
        font-size: 1.5em;
    }
    
    .markdown-unescape-content h2 {
        font-size: 1.3em;
    }
    
    .markdown-unescape-content h3 {
        font-size: 1.1em;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .markdown-unescape-container {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .markdown-unescape-header {
        color: #e2e8f0;
        border-bottom-color: #4a5568;
    }
    
    .markdown-unescape-content {
        color: #e2e8f0;
    }
    
    .markdown-unescape-content h1,
    .markdown-unescape-content h2,
    .markdown-unescape-content h3 {
        color: #f7fafc;
    }
    
    .markdown-unescape-content code {
        background: #2d3748;
        border-color: #4a5568;
        color: #ed8936;
    }
    
    .markdown-unescape-content pre {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .markdown-unescape-content blockquote {
        background: #2d3748;
        border-left-color: #4299e1;
        color: #cbd5e0;
    }
}
