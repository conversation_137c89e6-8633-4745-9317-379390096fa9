# Markdown Unescape Viewer Chrome Extension

A Chrome extension that automatically detects escaped markdown content on web pages and displays the unescaped, rendered markdown below the original content for better readability.

## Features

- 🔍 **Automatic Detection**: Scans web pages for escaped markdown content
- 🎨 **Beautiful Rendering**: Renders unescaped markdown with proper styling
- ⚡ **Real-time Processing**: <PERSON>les dynamically added content
- 🎛️ **Toggle Control**: Enable/disable functionality via popup
- 🌙 **Dark Mode Support**: Adapts to system color scheme
- 📱 **Responsive Design**: Works on all screen sizes

## What it detects

The extension identifies and processes escaped markdown patterns including:

- `\*bold text\*` → **bold text**
- `\_italic text\_` → _italic text_
- `\`inline code\`` → `inline code`
- `\# Headers` → # Headers
- `\[links\](url)` → [links](url)
- `\> blockquotes` → > blockquotes
- And more...

## Installation

### Method 1: Load as Unpacked Extension (Development)

1. **Download or clone this repository**
2. **Open Chrome** and navigate to `chrome://extensions/`
3. **Enable Developer mode** (toggle in top-right corner)
4. **Click "Load unpacked"** and select the extension directory
5. **The extension is now installed** and will appear in your extensions list

### Method 2: Manual Installation

1. **Download the extension files** to a local directory
2. **Ensure all required files are present**:
   - `manifest.json`
   - `content.js`
   - `styles.css`
   - `popup.html`
   - `popup.js`
   - `lib/marked.min.js`
   - `icons/` directory with icon files

## Usage

1. **Navigate to any webpage** with escaped markdown content
2. **The extension automatically scans** the page for escaped markdown (enabled by default)
3. **Rendered markdown appears** below the original escaped content
4. **Use the popup** (click extension icon) to toggle functionality on/off

### First Time Installation

- ✅ **Extension is enabled by default** - no setup required
- 🔄 **Works immediately** on supported pages
- 🎛️ **Toggle anytime** using the extension popup
- 📄 **Test with** `test-first-install.html` to verify installation

## Testing

### Standalone Test Page

Open `standalone-test.html` in your browser to test the functionality without installing the extension:

```bash
# Start a local server (if you have Python)
python3 -m http.server 8000

# Then open: http://localhost:8000/standalone-test.html
```

### Chrome Extension Test

1. Load the extension in Chrome (see Installation above)
2. Open `test-page.html` in your browser
3. Verify that escaped markdown content is detected and rendered

### Automated Testing

The project includes Playwright tests for automated testing:

```bash
# Install dependencies (requires Node.js)
npm install

# Run tests
npm test

# Run tests with browser visible
npm run test:headed
```

## Project Structure

```
chrome-plugin-unescape-v3/
├── manifest.json           # Chrome extension manifest
├── content.js             # Main content script
├── styles.css             # Styling for rendered markdown
├── popup.html             # Extension popup interface
├── popup.js               # Popup functionality
├── lib/
│   └── marked.min.js      # Markdown parsing library
├── icons/                 # Extension icons
│   ├── icon16.png
│   ├── icon48.png
│   └── icon128.png
├── test-page.html         # Test page with escaped markdown
├── standalone-test.html   # Standalone test (no extension needed)
├── tests/                 # Playwright test files
│   └── markdown-unescape.spec.js
├── playwright.config.js   # Playwright configuration
└── README.md             # This file
```

## How it Works

1. **Content Script Injection**: The extension injects a content script into all web pages
2. **DOM Scanning**: Uses TreeWalker to efficiently scan text nodes
3. **Pattern Matching**: Identifies escaped markdown using regex patterns
4. **Unescape Processing**: Removes escape characters from detected content
5. **Markdown Rendering**: Uses marked.js library to render markdown to HTML
6. **DOM Insertion**: Inserts rendered content below original text
7. **Mutation Observation**: Watches for dynamic content changes

## Configuration

The extension behavior can be customized by modifying the `CONFIG` object in `content.js`:

```javascript
const CONFIG = {
    minTextLength: 10,        // Minimum text length to process
    maxTextLength: 10000,     // Maximum text length to process
    debounceDelay: 500,       // Delay for processing dynamic content
    markdownPatterns: [...]   // Regex patterns for detection
};
```

## Browser Compatibility

- ✅ Chrome (Manifest V3)
- ✅ Chromium-based browsers
- ❓ Firefox (would require Manifest V2 adaptation)
- ❓ Safari (would require different extension format)

## Development

### Prerequisites

- Chrome browser
- Basic understanding of JavaScript and Chrome extensions
- Optional: Node.js for automated testing

### Making Changes

1. **Edit the source files** as needed
2. **Reload the extension** in Chrome (chrome://extensions/)
3. **Test your changes** on the test pages
4. **Run automated tests** if available

### Adding New Features

1. **Modify `content.js`** for core functionality
2. **Update `styles.css`** for styling changes
3. **Edit `popup.html/popup.js`** for UI changes
4. **Add tests** in the `tests/` directory

## Troubleshooting

### Extension Not Working

1. **Check Developer Console** for error messages
2. **Verify all files are present** and properly referenced
3. **Reload the extension** in chrome://extensions/
4. **Check permissions** in manifest.json

### Content Not Being Processed

1. **Verify the page has escaped markdown** (use test pages)
2. **Check if extension is enabled** via popup
3. **Look for console errors** in browser dev tools
4. **Ensure content meets minimum length requirements**

### Performance Issues

1. **Reduce `maxTextLength`** in CONFIG
2. **Increase `debounceDelay`** for slower processing
3. **Check for infinite loops** in mutation observer

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Changelog

### v1.0.0
- Initial release
- Basic escaped markdown detection
- Markdown rendering with marked.js
- Chrome extension popup interface
- Automated testing with Playwright
- Dark mode support
- Responsive design
