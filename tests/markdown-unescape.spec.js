const { test, expect } = require('@playwright/test');
const path = require('path');

test.describe('Markdown Unescape Extension', () => {
  let context;
  let page;

  test.beforeAll(async ({ browser }) => {
    // Create a new browser context with the extension loaded
    const extensionPath = path.resolve(__dirname, '..');
    context = await browser.newContext({
      // Load the extension
      args: [
        `--disable-extensions-except=${extensionPath}`,
        `--load-extension=${extensionPath}`,
      ],
    });
    
    page = await context.newPage();
  });

  test.afterAll(async () => {
    await context.close();
  });

  test('should load the test page', async () => {
    const testPagePath = path.resolve(__dirname, '../test-page.html');
    await page.goto(`file://${testPagePath}`);
    
    await expect(page).toHaveTitle('Markdown Unescape Test Page');
    await expect(page.locator('h1')).toContainText('Markdown Unescape Viewer Test Page');
  });

  test('should detect and render basic escaped formatting', async () => {
    const testPagePath = path.resolve(__dirname, '../test-page.html');
    await page.goto(`file://${testPagePath}`);
    
    // Wait for the extension to process the content
    await page.waitForTimeout(1000);
    
    // Check if markdown containers are created
    const markdownContainers = page.locator('.markdown-unescape-container');
    await expect(markdownContainers).toHaveCount({ min: 1 });
    
    // Check if the first test case is processed
    const firstContainer = markdownContainers.first();
    await expect(firstContainer).toBeVisible();
    
    // Check if the header is present
    const header = firstContainer.locator('.markdown-unescape-header');
    await expect(header).toContainText('Rendered Markdown');
    
    // Check if content is rendered
    const content = firstContainer.locator('.markdown-unescape-content');
    await expect(content).toBeVisible();
  });

  test('should render bold and italic text correctly', async () => {
    const testPagePath = path.resolve(__dirname, '../test-page.html');
    await page.goto(`file://${testPagePath}`);
    
    await page.waitForTimeout(1000);
    
    // Look for rendered bold text
    const boldElements = page.locator('.markdown-unescape-content strong, .markdown-unescape-content b');
    await expect(boldElements).toHaveCount({ min: 1 });
    
    // Look for rendered italic text
    const italicElements = page.locator('.markdown-unescape-content em, .markdown-unescape-content i');
    await expect(italicElements).toHaveCount({ min: 1 });
    
    // Look for rendered code elements
    const codeElements = page.locator('.markdown-unescape-content code');
    await expect(codeElements).toHaveCount({ min: 1 });
  });

  test('should render headers correctly', async () => {
    const testPagePath = path.resolve(__dirname, '../test-page.html');
    await page.goto(`file://${testPagePath}`);
    
    await page.waitForTimeout(1000);
    
    // Look for rendered headers
    const h1Elements = page.locator('.markdown-unescape-content h1');
    const h2Elements = page.locator('.markdown-unescape-content h2');
    const h3Elements = page.locator('.markdown-unescape-content h3');
    
    await expect(h1Elements.or(h2Elements).or(h3Elements)).toHaveCount({ min: 1 });
  });

  test('should handle dynamic content', async () => {
    const testPagePath = path.resolve(__dirname, '../test-page.html');
    await page.goto(`file://${testPagePath}`);
    
    // Click the button to add dynamic content
    await page.click('button:has-text("Add Dynamic Escaped Markdown")');
    
    // Wait for the extension to process the new content
    await page.waitForTimeout(1500);
    
    // Check if new markdown containers are created for dynamic content
    const markdownContainers = page.locator('.markdown-unescape-container');
    await expect(markdownContainers).toHaveCount({ min: 2 });
  });

  test('should not process regular markdown content', async () => {
    const testPagePath = path.resolve(__dirname, '../test-page.html');
    await page.goto(`file://${testPagePath}`);
    
    await page.waitForTimeout(1000);
    
    // Get all markdown containers
    const markdownContainers = page.locator('.markdown-unescape-container');
    const containerCount = await markdownContainers.count();
    
    // The "Should NOT Process" section should not generate a container
    // We expect fewer containers than test sections with escaped content
    expect(containerCount).toBeLessThan(9); // We have 9 test sections, but one shouldn't be processed
  });

  test('should handle auto-added delayed content', async () => {
    const testPagePath = path.resolve(__dirname, '../test-page.html');
    await page.goto(`file://${testPagePath}`);
    
    // Wait for the delayed content to be added (2 seconds + processing time)
    await page.waitForTimeout(3000);
    
    // Check if the delayed content was processed
    const delayedContent = page.locator('div:has-text("Auto-added after 2 seconds")');
    await expect(delayedContent).toBeVisible();
    
    // Check if markdown containers were created for the delayed content
    const markdownContainers = page.locator('.markdown-unescape-container');
    await expect(markdownContainers).toHaveCount({ min: 3 });
  });

  test('should have proper styling', async () => {
    const testPagePath = path.resolve(__dirname, '../test-page.html');
    await page.goto(`file://${testPagePath}`);
    
    await page.waitForTimeout(1000);
    
    const firstContainer = page.locator('.markdown-unescape-container').first();
    await expect(firstContainer).toBeVisible();
    
    // Check if the container has the expected styling
    const containerStyles = await firstContainer.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        border: styles.border,
        borderRadius: styles.borderRadius,
        padding: styles.padding,
        margin: styles.margin
      };
    });
    
    expect(containerStyles.borderRadius).toBe('8px');
    expect(containerStyles.padding).toBe('15px');
  });

  test('extension popup should be accessible', async () => {
    // This test would require additional setup to test the popup
    // For now, we'll just verify the extension is loaded
    const testPagePath = path.resolve(__dirname, '../test-page.html');
    await page.goto(`file://${testPagePath}`);
    
    // Check if the extension's content script is working
    await page.waitForTimeout(1000);
    const markdownContainers = page.locator('.markdown-unescape-container');
    await expect(markdownContainers).toHaveCount({ min: 1 });
  });
});
