<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Standalone Markdown Unescape Test</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .test-content {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
        
        .description {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            font-style: italic;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            background: #e3f2fd;
            border-radius: 8px;
            border: 1px solid #bbdefb;
        }
        
        .success {
            background: #e8f5e8;
            border-color: #c3e6c3;
            color: #2e7d32;
        }
        
        .error {
            background: #ffebee;
            border-color: #ffcdd2;
            color: #c62828;
        }
    </style>
</head>
<body>
    <h1>🧪 Standalone Markdown Unescape Test</h1>
    <p>This page tests the markdown unescape functionality without requiring a Chrome extension.</p>
    
    <div class="status" id="status">
        <strong>Status:</strong> Initializing...
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 1: Basic Escaped Formatting</div>
        <div class="description">Simple escaped bold, italic, and code formatting</div>
        <div class="test-content">
            This text contains \*bold text\* and \_italic text\_ and \`inline code\`.
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 2: Escaped Headers</div>
        <div class="description">Escaped markdown headers of different levels</div>
        <div class="test-content">
            \# This is an escaped H1 header<br>
            \## This is an escaped H2 header<br>
            \### This is an escaped H3 header
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 3: Complex Escaped Content</div>
        <div class="description">Mixed escaped markdown with multiple formatting types</div>
        <div class="test-content">
            \## Welcome to our \*\*amazing\*\* product!<br><br>
            
            Here are some key features:<br>
            \- \*Fast\* performance<br>
            \- \*\*Reliable\*\* service<br>
            \- \`Easy to use\` interface<br><br>
            
            Check out our \[documentation\]\(https://example.com\) for more details.
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 4: Dynamic Content Test</div>
        <div class="description">Test dynamic content addition</div>
        <div class="test-content" id="dynamic-content">
            <button onclick="addDynamicContent()">Add Dynamic Escaped Markdown</button>
        </div>
    </div>

    <!-- Load marked.js -->
    <script src="lib/marked.min.js"></script>
    
    <!-- Load our content script (modified for standalone use) -->
    <script>
        // Modified version of content.js for standalone testing
        (function() {
            'use strict';

            // Configuration
            const CONFIG = {
                minTextLength: 10,
                maxTextLength: 10000,
                debounceDelay: 500,
                markdownPatterns: [
                    /\\[\*_`#\[\]\\]/g,  // Escaped markdown characters
                    /\\\*[^*]+\\\*/g,    // Escaped bold/italic
                    /\\_[^_]+\\_/g,      // Escaped underscores
                    /\\`[^`]+\\`/g,      // Escaped code
                    /\\#{1,6}\s/g,       // Escaped headers
                    /\\\[[^\]]*\\\]/g    // Escaped links/references
                ]
            };

            // Track processed elements to avoid duplication
            const processedElements = new WeakSet();
            let isEnabled = true;
            let processedCount = 0;

            // Utility functions
            function unescapeMarkdown(text) {
                return text
                    .replace(/\\(\*)/g, '$1')
                    .replace(/\\(_)/g, '$1')
                    .replace(/\\(`)/g, '$1')
                    .replace(/\\(#)/g, '$1')
                    .replace(/\\(\[)/g, '$1')
                    .replace(/\\(\])/g, '$1')
                    .replace(/\\(\\)/g, '$1');
            }

            function hasEscapedMarkdown(text) {
                return CONFIG.markdownPatterns.some(pattern => pattern.test(text));
            }

            function isValidTextNode(node) {
                if (!node || node.nodeType !== Node.TEXT_NODE) return false;
                
                const text = node.textContent.trim();
                if (text.length < CONFIG.minTextLength || text.length > CONFIG.maxTextLength) return false;
                
                // Skip if parent is script, style, or already processed
                const parent = node.parentElement;
                if (!parent) return false;
                
                const tagName = parent.tagName.toLowerCase();
                if (['script', 'style', 'code', 'pre'].includes(tagName)) return false;
                
                return hasEscapedMarkdown(text);
            }

            function createMarkdownRenderer(originalElement, unescapedText) {
                const container = document.createElement('div');
                container.className = 'markdown-unescape-container';
                
                const header = document.createElement('div');
                header.className = 'markdown-unescape-header';
                header.innerHTML = '📝 Rendered Markdown';
                
                const content = document.createElement('div');
                content.className = 'markdown-unescape-content';
                
                try {
                    // Use marked.js to render markdown
                    if (typeof marked !== 'undefined') {
                        content.innerHTML = marked.parse(unescapedText);
                    } else {
                        // Fallback simple rendering
                        content.innerHTML = unescapedText
                            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                            .replace(/\*(.*?)\*/g, '<em>$1</em>')
                            .replace(/`(.*?)`/g, '<code>$1</code>')
                            .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                            .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                            .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                            .replace(/\n/g, '<br>');
                    }
                } catch (error) {
                    console.error('Markdown rendering error:', error);
                    content.textContent = unescapedText;
                }
                
                container.appendChild(header);
                container.appendChild(content);
                
                return container;
            }

            function processTextNode(textNode) {
                if (processedElements.has(textNode.parentElement)) return;
                
                const originalText = textNode.textContent;
                const unescapedText = unescapeMarkdown(originalText);
                
                if (originalText === unescapedText) return; // No changes needed
                
                const parent = textNode.parentElement;
                const renderer = createMarkdownRenderer(parent, unescapedText);
                
                // Insert after the parent element
                parent.parentNode.insertBefore(renderer, parent.nextSibling);
                processedElements.add(parent);
                processedCount++;
                
                console.log(`Processed text node ${processedCount}:`, {
                    original: originalText.substring(0, 50) + '...',
                    unescaped: unescapedText.substring(0, 50) + '...'
                });
            }

            function scanForMarkdown() {
                if (!isEnabled) return;
                
                const walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    {
                        acceptNode: function(node) {
                            return isValidTextNode(node) ? 
                                NodeFilter.FILTER_ACCEPT : 
                                NodeFilter.FILTER_REJECT;
                        }
                    }
                );

                const textNodes = [];
                let node;
                while (node = walker.nextNode()) {
                    textNodes.push(node);
                }

                textNodes.forEach(processTextNode);
                
                updateStatus();
            }

            function updateStatus() {
                const statusElement = document.getElementById('status');
                if (statusElement) {
                    const containerCount = document.querySelectorAll('.markdown-unescape-container').length;
                    statusElement.innerHTML = `<strong>Status:</strong> Processed ${processedCount} text nodes, created ${containerCount} markdown containers.`;
                    statusElement.className = containerCount > 0 ? 'status success' : 'status';
                }
            }

            // Debounced scan function
            let scanTimeout;
            function debouncedScan() {
                clearTimeout(scanTimeout);
                scanTimeout = setTimeout(scanForMarkdown, CONFIG.debounceDelay);
            }

            // Initialize
            function init() {
                console.log('Initializing Markdown Unescape functionality...');
                
                // Initial scan
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', scanForMarkdown);
                } else {
                    scanForMarkdown();
                }

                // Watch for dynamic content
                const observer = new MutationObserver(debouncedScan);
                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    characterData: true
                });
                
                console.log('Markdown Unescape initialized successfully');
            }

            // Global function for dynamic content testing
            window.addDynamicContent = function() {
                const container = document.getElementById('dynamic-content');
                const newContent = document.createElement('div');
                newContent.innerHTML = 'Dynamically added: \\*\\*Bold text\\*\\* and \\`code snippet\\`.';
                newContent.style.marginTop = '10px';
                newContent.style.padding = '10px';
                newContent.style.background = '#e3f2fd';
                newContent.style.borderRadius = '4px';
                container.appendChild(newContent);
            };

            // Start the functionality
            init();
        })();
    </script>
</body>
</html>
