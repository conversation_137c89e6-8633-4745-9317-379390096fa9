# Privacy Policy for Markdown Unescape Viewer

**Last updated: [Current Date]**

## Overview

Markdown Unescape Viewer ("we", "our", or "us") is committed to protecting your privacy. This Privacy Policy explains how our Chrome extension handles information when you use our service.

## Information We Do NOT Collect

We want to be clear about what we don't do:

- ❌ **No Personal Data Collection**: We do not collect, store, or transmit any personal information
- ❌ **No Browsing History**: We do not track or record your browsing history
- ❌ **No Website Content**: We do not store or transmit the content of websites you visit
- ❌ **No User Analytics**: We do not use analytics services or tracking tools
- ❌ **No Third-Party Services**: We do not integrate with any third-party services that collect data

## How the Extension Works

Our extension operates entirely locally on your device:

1. **Local Processing**: All markdown processing happens locally in your browser
2. **No Network Requests**: The extension does not make any network requests to external servers
3. **Temporary Processing**: Content is processed in memory and not stored permanently
4. **Local Storage Only**: Only your enable/disable preference is stored locally in your browser

## Permissions Explained

Our extension requests minimal permissions:

- **`activeTab`**: Allows the extension to access the current tab's content to detect and process escaped markdown
- **`storage`**: Stores your enable/disable preference locally in your browser

## Data Storage

The only data we store is:
- Your extension enable/disable preference (stored locally in your browser)
- This setting never leaves your device

## Third-Party Libraries

We use the following open-source library:
- **marked.js**: For markdown rendering (runs locally, no data transmission)

## Your Rights

Since we don't collect any personal data, there's no personal data to:
- Access
- Modify
- Delete
- Export

However, you can:
- Disable the extension at any time
- Uninstall the extension to remove all local data
- View the source code (open source)

## Security

- All processing happens locally on your device
- No data transmission to external servers
- No vulnerability to data breaches (no data collected)

## Children's Privacy

Our extension does not collect any information from anyone, including children under 13.

## Changes to This Policy

We may update this Privacy Policy from time to time. We will notify users of any changes by:
- Updating the "Last updated" date
- Posting the new policy in the extension's documentation

## Contact Us

If you have any questions about this Privacy Policy, please contact us at:
- Email: [<EMAIL>]
- GitHub: [your-github-repo]

## Open Source

This extension is open source. You can review the complete source code to verify our privacy practices at: [GitHub Repository URL]

---

**Summary**: We don't collect, store, or transmit any of your data. Everything happens locally on your device for your privacy and security.
