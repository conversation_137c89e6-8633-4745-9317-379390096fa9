// Test script for Chrome extension using Play<PERSON>
const { chromium } = require('playwright');
const path = require('path');

async function testExtension() {
    console.log('Starting Chrome extension test...');
    
    // Get the extension path
    const extensionPath = __dirname;
    console.log('Extension path:', extensionPath);
    
    // Launch browser with extension
    const browser = await chromium.launch({
        headless: false,
        args: [
            `--disable-extensions-except=${extensionPath}`,
            `--load-extension=${extensionPath}`,
            '--no-sandbox',
            '--disable-setuid-sandbox'
        ]
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Navigate to test page
    await page.goto('http://localhost:8000/test-page.html');
    console.log('Navigated to test page');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Wait for extension to process content
    await page.waitForTimeout(3000);
    
    // Check for markdown containers
    const containerCount = await page.evaluate(() => {
        const containers = document.querySelectorAll('.markdown-unescape-container');
        console.log(`Found ${containers.length} markdown containers`);
        
        containers.forEach((container, index) => {
            const header = container.querySelector('.markdown-unescape-header');
            const content = container.querySelector('.markdown-unescape-content');
            console.log(`Container ${index + 1}:`);
            console.log(`  Header: ${header ? header.textContent : 'Not found'}`);
            if (content) {
                console.log(`  Content HTML: ${content.innerHTML.substring(0, 200)}...`);
            }
        });
        
        return containers.length;
    });
    
    console.log(`Total containers found: ${containerCount}`);
    
    // Take a screenshot
    await page.screenshot({ path: 'extension-test-result.png', fullPage: true });
    console.log('Screenshot saved as extension-test-result.png');
    
    // Test dynamic content
    console.log('Testing dynamic content...');
    await page.click('button:has-text("Add Dynamic Escaped Markdown")');
    await page.waitForTimeout(2000);
    
    const newContainerCount = await page.evaluate(() => {
        return document.querySelectorAll('.markdown-unescape-container').length;
    });
    
    console.log(`Containers after dynamic content: ${newContainerCount}`);
    
    // Keep browser open for manual inspection
    console.log('Test completed. Browser will remain open for inspection.');
    console.log('Press Ctrl+C to close the browser and exit.');
    
    // Wait for user to close
    await new Promise(() => {}); // Keep running
}

// Run the test
testExtension().catch(console.error);
