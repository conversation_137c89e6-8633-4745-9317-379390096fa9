<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>First Install Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        .escaped-content {
            background: #fff3e0;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #ff9800;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>🔧 First Install Test Page</h1>
    
    <div class="instructions">
        <h3>测试步骤：</h3>
        <ol>
            <li><strong>首次安装插件</strong>后，直接访问此页面</li>
            <li><strong>检查是否自动处理</strong>下方的转义内容</li>
            <li><strong>点击插件图标</strong>，查看状态</li>
            <li><strong>切换开关</strong>，验证是否正常工作</li>
        </ol>
        
        <p><strong>预期行为：</strong></p>
        <ul>
            <li>✅ 插件默认启用，自动处理内容</li>
            <li>✅ 首次点击切换按钮应该正常工作</li>
            <li>✅ 不需要刷新页面</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Test 1: 基础转义内容</h2>
        <div class="escaped-content">
            这段文字包含 \*粗体文本\* 和 \_斜体文本\_ 以及 \`行内代码\`。
        </div>
    </div>

    <div class="test-section">
        <h2>Test 2: 标题转义</h2>
        <div class="escaped-content">
            \# 这是转义的一级标题
            \## 这是转义的二级标题
        </div>
    </div>

    <div class="test-section">
        <h2>Test 3: 复杂内容</h2>
        <div class="escaped-content">
            \## 功能特性\\n\\n1. \*\*自动检测\*\*\\n2. \*\*实时处理\*\*\\n3. \`易于使用\`
        </div>
    </div>

    <div class="test-section">
        <h2>Test 4: Pre标签内容</h2>
        <pre>\*这应该被处理\* 和 \_这也应该被处理\_</pre>
    </div>

    <script>
        console.log('First install test page loaded');
        console.log('Current time:', new Date().toLocaleTimeString());
        
        // Monitor for extension activity
        let checkCount = 0;
        const maxChecks = 10;
        
        function checkExtensionActivity() {
            checkCount++;
            const containers = document.querySelectorAll('.markdown-unescape-container');
            
            console.log(`Check ${checkCount}: Found ${containers.length} containers`);
            
            if (containers.length > 0) {
                console.log('✅ Extension is working!');
                return;
            }
            
            if (checkCount < maxChecks) {
                setTimeout(checkExtensionActivity, 1000);
            } else {
                console.log('❌ Extension may not be working or not installed');
            }
        }
        
        // Start monitoring after page load
        setTimeout(checkExtensionActivity, 1000);
    </script>
</body>
</html>
