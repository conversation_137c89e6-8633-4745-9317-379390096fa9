{"name": "chrome-plugin-unescape-v3", "version": "1.0.0", "description": "Chrome extension to detect and render escaped markdown content", "main": "content.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug"}, "keywords": ["chrome-extension", "markdown", "unescape", "browser-extension"], "author": "", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0"}}