<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Unescape Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .test-content {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
        
        .description {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            font-style: italic;
        }
        
        code {
            background: #f1f3f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', '<PERSON><PERSON>', monospace;
        }
    </style>
</head>
<body>
    <h1>🧪 Markdown Unescape Viewer Test Page</h1>
    <p>This page contains various examples of escaped markdown content to test the Chrome extension.</p>
    
    <div class="test-section">
        <div class="test-title">Test 1: Basic Escaped Formatting</div>
        <div class="description">Simple escaped bold, italic, and code formatting</div>
        <div class="test-content">
            This text contains \*bold text\* and \_italic text\_ and \`inline code\`.
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 2: Escaped Headers</div>
        <div class="description">Escaped markdown headers of different levels</div>
        <div class="test-content">
            \# This is an escaped H1 header
            \## This is an escaped H2 header
            \### This is an escaped H3 header
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 3: Complex Escaped Content</div>
        <div class="description">Mixed escaped markdown with multiple formatting types</div>
        <div class="test-content">
            \## Welcome to our \*\*amazing\*\* product!
            
            Here are some key features:
            \- \*Fast\* performance
            \- \*\*Reliable\*\* service  
            \- \`Easy to use\` interface
            
            Check out our \[documentation\]\(https://example.com\) for more details.
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 4: Escaped Links and References</div>
        <div class="description">Escaped markdown links and reference-style links</div>
        <div class="test-content">
            Visit our \[website\]\(https://example.com\) or check the \[API docs\]\[1\].
            
            \[1\]: https://api.example.com
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 5: Escaped Code Blocks</div>
        <div class="description">Escaped code blocks with backticks</div>
        <div class="test-content">
            Here's some escaped code:
            \`\`\`javascript
            function hello() {
                console.log("Hello, world!");
            }
            \`\`\`
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 6: Mixed Content</div>
        <div class="description">Regular text mixed with escaped markdown</div>
        <div class="test-content">
            This is normal text. But this part has \*escaped formatting\* and this \`escaped code\`. 
            The rest is normal again.
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 7: Escaped Blockquotes</div>
        <div class="description">Escaped blockquote syntax</div>
        <div class="test-content">
            \> This is an escaped blockquote
            \> with multiple lines
            \> and \*formatting\* inside
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 8: Escaped Lists</div>
        <div class="description">Escaped list items</div>
        <div class="test-content">
            Shopping list:
            \* Buy \*\*milk\*\*
            \* Get \`bread\`
            \* Pick up \_vegetables\_
            
            Numbered list:
            1\. First item
            2\. Second item with \[link\]\(https://example.com\)
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 9: Should NOT Process</div>
        <div class="description">This content should NOT be processed (no escaped markdown)</div>
        <div class="test-content">
            This is just normal text with *actual* markdown formatting and `real` code snippets.
            ## This is a real header
            **This is real bold text**
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">Test 10: Dynamic Content</div>
        <div class="description">Content that will be added dynamically</div>
        <div class="test-content" id="dynamic-content">
            <button onclick="addDynamicContent()">Add Dynamic Escaped Markdown</button>
        </div>
    </div>
    
    <script>
        function addDynamicContent() {
            const container = document.getElementById('dynamic-content');
            const newContent = document.createElement('div');
            newContent.innerHTML = 'Dynamically added: \\*\\*Bold text\\*\\* and \\`code snippet\\`.';
            newContent.style.marginTop = '10px';
            newContent.style.padding = '10px';
            newContent.style.background = '#e3f2fd';
            newContent.style.borderRadius = '4px';
            container.appendChild(newContent);
        }
        
        // Add some content after page load to test mutation observer
        setTimeout(() => {
            const container = document.getElementById('dynamic-content');
            const delayedContent = document.createElement('div');
            delayedContent.innerHTML = 'Auto-added after 2 seconds: \\# Escaped Header and \\_italic text\\_.';
            delayedContent.style.marginTop = '10px';
            delayedContent.style.padding = '10px';
            delayedContent.style.background = '#fff3cd';
            delayedContent.style.borderRadius = '4px';
            container.appendChild(delayedContent);
        }, 2000);
    </script>
</body>
</html>
