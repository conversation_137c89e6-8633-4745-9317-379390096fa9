<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed Extension Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            border: 1px solid #e9ecef;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 Fixed Extension Test</h1>
    <p>This page tests the fixed version that should process multiple content blocks.</p>

    <div class="test-section">
        <h2>Test 1: Simple Markdown</h2>
        <pre># Hello World\\n\\nThis is **bold** and *italic* text.\\n\\n- Item 1\\n- Item 2</pre>
    </div>

    <div class="test-section">
        <h2>Test 2: Another Markdown Block</h2>
        <pre>## Features\\n\\n1. **Auto-detection**\\n2. **Multi-element processing**\\n3. **Better escape handling**\\n\\n> This should work now!</pre>
    </div>

    <div class="test-section">
        <h2>Test 3: JSON Content</h2>
        <pre>{\"name\": \"Test\", \"value\": \"Hello\\nWorld\", \"items\": [1, 2, 3]}</pre>
    </div>

    <div class="test-section">
        <h2>Test 4: Complex Markdown</h2>
        <pre>### Code Example\\n\\n```javascript\\nfunction test() {\\n    console.log(\\\"Hello!\\\");\\n}\\n```\\n\\n| Col1 | Col2 |\\n|------|------|\\n| A    | B    |</pre>
    </div>

    <script>
        console.log('Test page loaded. Check console for extension debug messages.');
        
        // Add some debugging
        setTimeout(() => {
            const containers = document.querySelectorAll('.unescape-formatter-container');
            console.log(`Found ${containers.length} processed containers`);
        }, 2000);
    </script>
</body>
</html>
