// Markdown Unescape Viewer - Production Version
(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        minTextLength: 10,
        maxTextLength: 10000,
        debounceDelay: 500,
        processPre: true,  // Allow processing <pre> tags
        processCode: false, // Skip <code> tags
        markdownPatterns: [
            /\\[\*_`#\[\]\\]/g,  // Escaped markdown characters
            /\\\*[^*]+\\\*/g,    // Escaped bold/italic
            /\\_[^_]+\\_/g,      // Escaped underscores
            /\\`[^`]+\\`/g,      // Escaped code
            /\\#{1,6}\s/g,       // Escaped headers
            /\\\[[^\]]*\\\]/g,   // Escaped links/references
            /\\n/g,              // Escaped newlines
            /\\t/g,              // Escaped tabs
            /\\r/g               // Escaped carriage returns
        ]
    };

    // Track processed elements to avoid duplication
    let processedElements = new Set();
    let isEnabled = true;
    let isInitialized = false;

    // Utility functions
    function unescapeMarkdown(text) {
        return text
            .replace(/\\(\*)/g, '$1')
            .replace(/\\(_)/g, '$1')
            .replace(/\\(`)/g, '$1')
            .replace(/\\(#)/g, '$1')
            .replace(/\\(\[)/g, '$1')
            .replace(/\\(\])/g, '$1')
            .replace(/\\(\\)/g, '$1')
            .replace(/\\n/g, '\n')
            .replace(/\\t/g, '\t')
            .replace(/\\r/g, '\r');
    }

    function hasEscapedMarkdown(text) {
        return CONFIG.markdownPatterns.some(pattern => pattern.test(text));
    }

    function isValidTextNode(node) {
        if (!node || node.nodeType !== Node.TEXT_NODE) return false;
        
        const text = node.textContent.trim();
        if (text.length < CONFIG.minTextLength || text.length > CONFIG.maxTextLength) return false;
        
        const parent = node.parentElement;
        if (!parent) return false;
        
        const tagName = parent.tagName.toLowerCase();
        
        // Always skip script and style
        if (['script', 'style'].includes(tagName)) return false;
        
        // Conditionally skip code and pre based on config
        if (!CONFIG.processCode && tagName === 'code') return false;
        if (!CONFIG.processPre && tagName === 'pre') return false;
        
        return hasEscapedMarkdown(text);
    }

    function createMarkdownRenderer(originalElement, unescapedText) {
        const container = document.createElement('div');
        container.className = 'markdown-unescape-container';
        
        const header = document.createElement('div');
        header.className = 'markdown-unescape-header';
        header.innerHTML = '📝 Rendered Markdown';
        
        const content = document.createElement('div');
        content.className = 'markdown-unescape-content';
        
        try {
            if (typeof marked !== 'undefined') {
                content.innerHTML = marked.parse(unescapedText);
            } else {
                // Fallback simple rendering
                content.innerHTML = unescapedText
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/`(.*?)`/g, '<code>$1</code>')
                    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                    .replace(/\n/g, '<br>');
            }
        } catch (error) {
            content.textContent = unescapedText;
        }
        
        container.appendChild(header);
        container.appendChild(content);
        
        return container;
    }

    function processTextNode(textNode) {
        const parent = textNode.parentElement;
        if (processedElements.has(parent)) return;
        
        const originalText = textNode.textContent;
        const unescapedText = unescapeMarkdown(originalText);
        
        if (originalText === unescapedText) return;
        
        const renderer = createMarkdownRenderer(parent, unescapedText);
        parent.parentNode.insertBefore(renderer, parent.nextSibling);
        processedElements.add(parent);
    }

    function removeAllMarkdownContainers() {
        const containers = document.querySelectorAll('.markdown-unescape-container');
        containers.forEach(container => container.remove());
        processedElements.clear();
    }

    function scanForMarkdown() {
        if (!isEnabled) {
            removeAllMarkdownContainers();
            return;
        }
        
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: function(node) {
                    return isValidTextNode(node) ? 
                        NodeFilter.FILTER_ACCEPT : 
                        NodeFilter.FILTER_REJECT;
                }
            }
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        textNodes.forEach(processTextNode);
    }

    // Debounced scan function
    let scanTimeout;
    function debouncedScan() {
        clearTimeout(scanTimeout);
        scanTimeout = setTimeout(scanForMarkdown, CONFIG.debounceDelay);
    }

    // Initialize with stored preferences
    function initializeWithStoredState() {
        chrome.storage.local.get(['extensionEnabled'], function(result) {
            isEnabled = result.extensionEnabled !== false;
            isInitialized = true;
            
            if (isEnabled) {
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', scanForMarkdown);
                } else {
                    scanForMarkdown();
                }
            }
        });
    }

    // Initialize
    function init() {
        initializeWithStoredState();

        // Watch for dynamic content
        const observer = new MutationObserver(debouncedScan);
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });

        // Listen for messages from popup
        chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
            if (request.action === 'toggle') {
                isEnabled = !isEnabled;
                chrome.storage.local.set({extensionEnabled: isEnabled});
                
                if (isEnabled) {
                    scanForMarkdown();
                } else {
                    removeAllMarkdownContainers();
                }
                
                sendResponse({enabled: isEnabled});
            } else if (request.action === 'getStatus') {
                sendResponse({enabled: isEnabled});
            }
        });
    }

    // Start the extension
    init();
})();
